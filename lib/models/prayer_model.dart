import 'package:objectbox/objectbox.dart';
import 'xp_history_item_model.dart';

@Entity()
class Prayer {
  @Id()
  int id = 0;

  // Prayer name (<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>)
  String name;

  // Status of the prayer (completed or not)
  bool isCompleted;

  // Date of the prayer record
  @Property(type: PropertyType.date)
  DateTime date;

  // Relation to XP history item
  final xpHistory = ToOne<XPHistoryItem>();

  // Constructor
  Prayer({
    this.id = 0,
    required this.name,
    this.isCompleted = false,
    DateTime? date,
  }) : date = date ?? DateTime.now();

  // Copy with method for immutability
  Prayer copyWith({int? id, String? name, bool? isCompleted, DateTime? date}) {
    return Prayer(
      id: id ?? this.id,
      name: name ?? this.name,
      isCompleted: isCompleted ?? this.isCompleted,
      date: date ?? this.date,
    );
  }

  @override
  String toString() {
    return 'Prayer(id: $id, name: $name, isCompleted: $isCompleted, date: $date)';
  }
}
