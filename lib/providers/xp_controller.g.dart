// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'xp_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

/// A controller that handles XP-related operations
@ProviderFor(XPController)
const xPControllerProvider = XPControllerProvider._();

/// A controller that handles XP-related operations
final class XPControllerProvider
    extends $AsyncNotifierProvider<XPController, void> {
  /// A controller that handles XP-related operations
  const XPControllerProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'xPControllerProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$xPControllerHash();

  @$internal
  @override
  XPController create() => XPController();
}

String _$xPControllerHash() => r'470c518743a9d21003f05fdaa4f647af4a971168';

abstract class _$XPController extends $AsyncNotifier<void> {
  FutureOr<void> build();
  @$mustCallSuper
  @override
  void runBuild() {
    build();
    final ref = this.ref as $Ref<AsyncValue<void>, void>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<AsyncValue<void>, void>,
              AsyncValue<void>,
              Object?,
              Object?
            >;
    element.handleValue(ref, null);
  }
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
