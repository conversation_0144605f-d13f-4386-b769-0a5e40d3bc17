// GENERATED CODE - DO NOT MODIFY BY HAND
// This code was generated by ObjectBox. To update it run the generator again
// with `dart run build_runner build`.
// See also https://docs.objectbox.io/getting-started#generate-objectbox-code

// ignore_for_file: camel_case_types, depend_on_referenced_packages
// coverage:ignore-file

import 'dart:typed_data';

import 'package:flat_buffers/flat_buffers.dart' as fb;
import 'package:objectbox/internal.dart'
    as obx_int; // generated code can access "internal" functionality
import 'package:objectbox/objectbox.dart' as obx;
import 'package:objectbox_flutter_libs/objectbox_flutter_libs.dart';

import 'models/challenge_model.dart';
import 'models/prayer_model.dart';
import 'models/streak_model.dart';
import 'models/temptation_model.dart';
import 'models/user_profile_model.dart';
import 'models/xp_history_item_model.dart';

export 'package:objectbox/objectbox.dart'; // so that callers only have to import this file

final _entities = <obx_int.ModelEntity>[
  obx_int.ModelEntity(
    id: const obx_int.IdUid(1, 1338698365441690232),
    name: 'Prayer',
    lastPropertyId: const obx_int.IdUid(5, 8161649086128211093),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(1, 1781763481287396281),
        name: 'id',
        type: 6,
        flags: 1,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 6676940911030062734),
        name: 'name',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(3, 3906021497468251084),
        name: 'isCompleted',
        type: 1,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(4, 4985978825233514570),
        name: 'date',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(5, 8161649086128211093),
        name: 'xpHistoryId',
        type: 11,
        flags: 520,
        indexId: const obx_int.IdUid(1, 1128823597899153944),
        relationTarget: 'XPHistoryItem',
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[],
  ),
  obx_int.ModelEntity(
    id: const obx_int.IdUid(2, 706345959104340872),
    name: 'Streak',
    lastPropertyId: const obx_int.IdUid(11, 9152171727215301684),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(1, 86702272585025853),
        name: 'id',
        type: 6,
        flags: 1,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 8786729964557317049),
        name: 'count',
        type: 6,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(3, 4987530681952276612),
        name: 'goal',
        type: 6,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(5, 7916892743323186033),
        name: 'date',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(7, 4931378688834158035),
        name: 'moodIntensity',
        type: 6,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(8, 1720440084525908862),
        name: 'createdAt',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(9, 2608692504366397466),
        name: 'lastUpdated',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(10, 6392053059665417157),
        name: 'isSuccess',
        type: 1,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(11, 9152171727215301684),
        name: 'xpHistoryId',
        type: 11,
        flags: 520,
        indexId: const obx_int.IdUid(2, 632438745793644113),
        relationTarget: 'XPHistoryItem',
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[],
  ),
  obx_int.ModelEntity(
    id: const obx_int.IdUid(3, 8512291693082550534),
    name: 'UserProfile',
    lastPropertyId: const obx_int.IdUid(13, 1892833832546443536),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(1, 7626070384926860621),
        name: 'id',
        type: 6,
        flags: 129,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 4919641217540815294),
        name: 'name',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(3, 671748751485565877),
        name: 'goals',
        type: 30,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(4, 7411803757707392293),
        name: 'hobbies',
        type: 30,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(5, 6796478754985101917),
        name: 'triggers',
        type: 30,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(6, 3751846143681267266),
        name: 'streakGoal',
        type: 6,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(7, 7587397390803279660),
        name: 'passwordHash',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(8, 1708789767381654030),
        name: 'biometricEnabled',
        type: 1,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(9, 3497005368154987552),
        name: 'notificationsEnabled',
        type: 1,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(10, 7007787891191731417),
        name: 'createdAt',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(11, 2007209796577511304),
        name: 'lastUpdated',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(12, 4431811609107151357),
        name: 'profilePicture',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(13, 1892833832546443536),
        name: 'xp',
        type: 6,
        flags: 0,
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[],
  ),
  obx_int.ModelEntity(
    id: const obx_int.IdUid(4, 5989821107332366666),
    name: 'Temptation',
    lastPropertyId: const obx_int.IdUid(11, 4593547370337186821),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(1, 5807834710776675500),
        name: 'id',
        type: 6,
        flags: 1,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 800209060057624311),
        name: 'createdAt',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(3, 1001322132879716430),
        name: 'resolvedAt',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(4, 746436749714338737),
        name: 'triggers',
        type: 30,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(5, 1664716629593652870),
        name: 'helpfulActivities',
        type: 30,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(6, 7160416427246840258),
        name: 'selectedActivity',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(7, 4688014462615064624),
        name: 'wasSuccessful',
        type: 1,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(8, 501155006114622597),
        name: 'resolutionNotes',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(9, 2834688869763168674),
        name: 'intensityBefore',
        type: 6,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(10, 413574071793923485),
        name: 'intensityAfter',
        type: 6,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(11, 4593547370337186821),
        name: 'xpHistoryId',
        type: 11,
        flags: 520,
        indexId: const obx_int.IdUid(3, 374482867967219037),
        relationTarget: 'XPHistoryItem',
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[],
  ),
  obx_int.ModelEntity(
    id: const obx_int.IdUid(5, 8320926897872393874),
    name: 'XPHistoryItem',
    lastPropertyId: const obx_int.IdUid(4, 6389020876003628461),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(1, 926498438497895275),
        name: 'id',
        type: 6,
        flags: 1,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 6356618502041027489),
        name: 'amount',
        type: 6,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(3, 8537177048229919959),
        name: 'description',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(4, 6389020876003628461),
        name: 'createdAt',
        type: 10,
        flags: 0,
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[],
  ),
  obx_int.ModelEntity(
    id: const obx_int.IdUid(6, 6291498019752657236),
    name: 'Challenge',
    lastPropertyId: const obx_int.IdUid(10, 6978369473478093011),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(1, 3972189838365105288),
        name: 'id',
        type: 6,
        flags: 1,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 7387352861388391673),
        name: 'title',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(3, 1048566736513530322),
        name: 'description',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(4, 1109013356722225666),
        name: 'featureName',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(5, 4925002412145861563),
        name: 'conditionJson',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(6, 5168496962090283635),
        name: 'iconPath',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(7, 5442020530038481552),
        name: 'isCompleted',
        type: 1,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(8, 5633638316268850998),
        name: 'completedAt',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(9, 7846699043998571435),
        name: 'xp',
        type: 6,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(10, 6978369473478093011),
        name: 'xpHistoryId',
        type: 11,
        flags: 520,
        indexId: const obx_int.IdUid(4, 3964634972446993293),
        relationTarget: 'XPHistoryItem',
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[],
  ),
];

/// Shortcut for [obx.Store.new] that passes [getObjectBoxModel] and for Flutter
/// apps by default a [directory] using `defaultStoreDirectory()` from the
/// ObjectBox Flutter library.
///
/// Note: for desktop apps it is recommended to specify a unique [directory].
///
/// See [obx.Store.new] for an explanation of all parameters.
///
/// For Flutter apps, also calls `loadObjectBoxLibraryAndroidCompat()` from
/// the ObjectBox Flutter library to fix loading the native ObjectBox library
/// on Android 6 and older.
Future<obx.Store> openStore({
  String? directory,
  int? maxDBSizeInKB,
  int? maxDataSizeInKB,
  int? fileMode,
  int? maxReaders,
  bool queriesCaseSensitiveDefault = true,
  String? macosApplicationGroup,
}) async {
  await loadObjectBoxLibraryAndroidCompat();
  return obx.Store(
    getObjectBoxModel(),
    directory: directory ?? (await defaultStoreDirectory()).path,
    maxDBSizeInKB: maxDBSizeInKB,
    maxDataSizeInKB: maxDataSizeInKB,
    fileMode: fileMode,
    maxReaders: maxReaders,
    queriesCaseSensitiveDefault: queriesCaseSensitiveDefault,
    macosApplicationGroup: macosApplicationGroup,
  );
}

/// Returns the ObjectBox model definition for this project for use with
/// [obx.Store.new].
obx_int.ModelDefinition getObjectBoxModel() {
  final model = obx_int.ModelInfo(
    entities: _entities,
    lastEntityId: const obx_int.IdUid(6, 6291498019752657236),
    lastIndexId: const obx_int.IdUid(4, 3964634972446993293),
    lastRelationId: const obx_int.IdUid(0, 0),
    lastSequenceId: const obx_int.IdUid(0, 0),
    retiredEntityUids: const [],
    retiredIndexUids: const [],
    retiredPropertyUids: const [6437257444821216180, 85010084430651590],
    retiredRelationUids: const [],
    modelVersion: 5,
    modelVersionParserMinimum: 5,
    version: 1,
  );

  final bindings = <Type, obx_int.EntityDefinition>{
    Prayer: obx_int.EntityDefinition<Prayer>(
      model: _entities[0],
      toOneRelations: (Prayer object) => [object.xpHistory],
      toManyRelations: (Prayer object) => {},
      getId: (Prayer object) => object.id,
      setId: (Prayer object, int id) {
        object.id = id;
      },
      objectToFB: (Prayer object, fb.Builder fbb) {
        final nameOffset = fbb.writeString(object.name);
        fbb.startTable(6);
        fbb.addInt64(0, object.id);
        fbb.addOffset(1, nameOffset);
        fbb.addBool(2, object.isCompleted);
        fbb.addInt64(3, object.date.millisecondsSinceEpoch);
        fbb.addInt64(4, object.xpHistory.targetId);
        fbb.finish(fbb.endTable());
        return object.id;
      },
      objectFromFB: (obx.Store store, ByteData fbData) {
        final buffer = fb.BufferContext(fbData);
        final rootOffset = buffer.derefObject(0);
        final idParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          4,
          0,
        );
        final nameParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 6, '');
        final isCompletedParam = const fb.BoolReader().vTableGet(
          buffer,
          rootOffset,
          8,
          false,
        );
        final dateParam = DateTime.fromMillisecondsSinceEpoch(
          const fb.Int64Reader().vTableGet(buffer, rootOffset, 10, 0),
        );
        final object = Prayer(
          id: idParam,
          name: nameParam,
          isCompleted: isCompletedParam,
          date: dateParam,
        );
        object.xpHistory.targetId = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          12,
          0,
        );
        object.xpHistory.attach(store);
        return object;
      },
    ),
    Streak: obx_int.EntityDefinition<Streak>(
      model: _entities[1],
      toOneRelations: (Streak object) => [object.xpHistory],
      toManyRelations: (Streak object) => {},
      getId: (Streak object) => object.id,
      setId: (Streak object, int id) {
        object.id = id;
      },
      objectToFB: (Streak object, fb.Builder fbb) {
        fbb.startTable(12);
        fbb.addInt64(0, object.id);
        fbb.addInt64(1, object.count);
        fbb.addInt64(2, object.goal);
        fbb.addInt64(4, object.date.millisecondsSinceEpoch);
        fbb.addInt64(6, object.moodIntensity);
        fbb.addInt64(7, object.createdAt.millisecondsSinceEpoch);
        fbb.addInt64(8, object.lastUpdated.millisecondsSinceEpoch);
        fbb.addBool(9, object.isSuccess);
        fbb.addInt64(10, object.xpHistory.targetId);
        fbb.finish(fbb.endTable());
        return object.id;
      },
      objectFromFB: (obx.Store store, ByteData fbData) {
        final buffer = fb.BufferContext(fbData);
        final rootOffset = buffer.derefObject(0);
        final idParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          4,
          0,
        );
        final countParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          6,
          0,
        );
        final goalParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          8,
          0,
        );
        final moodIntensityParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          16,
          0,
        );
        final isSuccessParam = const fb.BoolReader().vTableGet(
          buffer,
          rootOffset,
          22,
          false,
        );
        final dateParam = DateTime.fromMillisecondsSinceEpoch(
          const fb.Int64Reader().vTableGet(buffer, rootOffset, 12, 0),
        );
        final createdAtParam = DateTime.fromMillisecondsSinceEpoch(
          const fb.Int64Reader().vTableGet(buffer, rootOffset, 18, 0),
        );
        final lastUpdatedParam = DateTime.fromMillisecondsSinceEpoch(
          const fb.Int64Reader().vTableGet(buffer, rootOffset, 20, 0),
        );
        final object = Streak(
          id: idParam,
          count: countParam,
          goal: goalParam,
          moodIntensity: moodIntensityParam,
          isSuccess: isSuccessParam,
          date: dateParam,
          createdAt: createdAtParam,
          lastUpdated: lastUpdatedParam,
        );
        object.xpHistory.targetId = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          24,
          0,
        );
        object.xpHistory.attach(store);
        return object;
      },
    ),
    UserProfile: obx_int.EntityDefinition<UserProfile>(
      model: _entities[2],
      toOneRelations: (UserProfile object) => [],
      toManyRelations: (UserProfile object) => {},
      getId: (UserProfile object) => object.id,
      setId: (UserProfile object, int id) {
        object.id = id;
      },
      objectToFB: (UserProfile object, fb.Builder fbb) {
        final nameOffset = fbb.writeString(object.name);
        final goalsOffset = fbb.writeList(
          object.goals.map(fbb.writeString).toList(growable: false),
        );
        final hobbiesOffset = fbb.writeList(
          object.hobbies.map(fbb.writeString).toList(growable: false),
        );
        final triggersOffset = fbb.writeList(
          object.triggers.map(fbb.writeString).toList(growable: false),
        );
        final passwordHashOffset = fbb.writeString(object.passwordHash);
        final profilePictureOffset = fbb.writeString(object.profilePicture);
        fbb.startTable(14);
        fbb.addInt64(0, object.id);
        fbb.addOffset(1, nameOffset);
        fbb.addOffset(2, goalsOffset);
        fbb.addOffset(3, hobbiesOffset);
        fbb.addOffset(4, triggersOffset);
        fbb.addInt64(5, object.streakGoal);
        fbb.addOffset(6, passwordHashOffset);
        fbb.addBool(7, object.biometricEnabled);
        fbb.addBool(8, object.notificationsEnabled);
        fbb.addInt64(9, object.createdAt.millisecondsSinceEpoch);
        fbb.addInt64(10, object.lastUpdated.millisecondsSinceEpoch);
        fbb.addOffset(11, profilePictureOffset);
        fbb.addInt64(12, object.xp);
        fbb.finish(fbb.endTable());
        return object.id;
      },
      objectFromFB: (obx.Store store, ByteData fbData) {
        final buffer = fb.BufferContext(fbData);
        final rootOffset = buffer.derefObject(0);
        final idParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          4,
          0,
        );
        final nameParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 6, '');
        final goalsParam = const fb.ListReader<String>(
          fb.StringReader(asciiOptimization: true),
          lazy: false,
        ).vTableGet(buffer, rootOffset, 8, []);
        final hobbiesParam = const fb.ListReader<String>(
          fb.StringReader(asciiOptimization: true),
          lazy: false,
        ).vTableGet(buffer, rootOffset, 10, []);
        final triggersParam = const fb.ListReader<String>(
          fb.StringReader(asciiOptimization: true),
          lazy: false,
        ).vTableGet(buffer, rootOffset, 12, []);
        final streakGoalParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          14,
          0,
        );
        final passwordHashParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 16, '');
        final biometricEnabledParam = const fb.BoolReader().vTableGet(
          buffer,
          rootOffset,
          18,
          false,
        );
        final notificationsEnabledParam = const fb.BoolReader().vTableGet(
          buffer,
          rootOffset,
          20,
          false,
        );
        final profilePictureParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 26, '');
        final xpParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          28,
          0,
        );
        final createdAtParam = DateTime.fromMillisecondsSinceEpoch(
          const fb.Int64Reader().vTableGet(buffer, rootOffset, 22, 0),
        );
        final lastUpdatedParam = DateTime.fromMillisecondsSinceEpoch(
          const fb.Int64Reader().vTableGet(buffer, rootOffset, 24, 0),
        );
        final object = UserProfile(
          id: idParam,
          name: nameParam,
          goals: goalsParam,
          hobbies: hobbiesParam,
          triggers: triggersParam,
          streakGoal: streakGoalParam,
          passwordHash: passwordHashParam,
          biometricEnabled: biometricEnabledParam,
          notificationsEnabled: notificationsEnabledParam,
          profilePicture: profilePictureParam,
          xp: xpParam,
          createdAt: createdAtParam,
          lastUpdated: lastUpdatedParam,
        );

        return object;
      },
    ),
    Temptation: obx_int.EntityDefinition<Temptation>(
      model: _entities[3],
      toOneRelations: (Temptation object) => [object.xpHistory],
      toManyRelations: (Temptation object) => {},
      getId: (Temptation object) => object.id,
      setId: (Temptation object, int id) {
        object.id = id;
      },
      objectToFB: (Temptation object, fb.Builder fbb) {
        final triggersOffset = fbb.writeList(
          object.triggers.map(fbb.writeString).toList(growable: false),
        );
        final helpfulActivitiesOffset = fbb.writeList(
          object.helpfulActivities.map(fbb.writeString).toList(growable: false),
        );
        final selectedActivityOffset = object.selectedActivity == null
            ? null
            : fbb.writeString(object.selectedActivity!);
        final resolutionNotesOffset = object.resolutionNotes == null
            ? null
            : fbb.writeString(object.resolutionNotes!);
        fbb.startTable(12);
        fbb.addInt64(0, object.id);
        fbb.addInt64(1, object.createdAt.millisecondsSinceEpoch);
        fbb.addInt64(2, object.resolvedAt?.millisecondsSinceEpoch);
        fbb.addOffset(3, triggersOffset);
        fbb.addOffset(4, helpfulActivitiesOffset);
        fbb.addOffset(5, selectedActivityOffset);
        fbb.addBool(6, object.wasSuccessful);
        fbb.addOffset(7, resolutionNotesOffset);
        fbb.addInt64(8, object.intensityBefore);
        fbb.addInt64(9, object.intensityAfter);
        fbb.addInt64(10, object.xpHistory.targetId);
        fbb.finish(fbb.endTable());
        return object.id;
      },
      objectFromFB: (obx.Store store, ByteData fbData) {
        final buffer = fb.BufferContext(fbData);
        final rootOffset = buffer.derefObject(0);
        final resolvedAtValue = const fb.Int64Reader().vTableGetNullable(
          buffer,
          rootOffset,
          8,
        );
        final idParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          4,
          0,
        );
        final createdAtParam = DateTime.fromMillisecondsSinceEpoch(
          const fb.Int64Reader().vTableGet(buffer, rootOffset, 6, 0),
        );
        final wasSuccessfulParam = const fb.BoolReader().vTableGet(
          buffer,
          rootOffset,
          16,
          false,
        );
        final intensityBeforeParam = const fb.Int64Reader().vTableGetNullable(
          buffer,
          rootOffset,
          20,
        );
        final intensityAfterParam = const fb.Int64Reader().vTableGetNullable(
          buffer,
          rootOffset,
          22,
        );
        final resolutionNotesParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGetNullable(buffer, rootOffset, 18);
        final selectedActivityParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGetNullable(buffer, rootOffset, 14);
        final triggersParam = const fb.ListReader<String>(
          fb.StringReader(asciiOptimization: true),
          lazy: false,
        ).vTableGet(buffer, rootOffset, 10, []);
        final helpfulActivitiesParam = const fb.ListReader<String>(
          fb.StringReader(asciiOptimization: true),
          lazy: false,
        ).vTableGet(buffer, rootOffset, 12, []);
        final object =
            Temptation(
                id: idParam,
                createdAt: createdAtParam,
                wasSuccessful: wasSuccessfulParam,
                intensityBefore: intensityBeforeParam,
                intensityAfter: intensityAfterParam,
                resolutionNotes: resolutionNotesParam,
                selectedActivity: selectedActivityParam,
                triggers: triggersParam,
                helpfulActivities: helpfulActivitiesParam,
              )
              ..resolvedAt = resolvedAtValue == null
                  ? null
                  : DateTime.fromMillisecondsSinceEpoch(resolvedAtValue);
        object.xpHistory.targetId = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          24,
          0,
        );
        object.xpHistory.attach(store);
        return object;
      },
    ),
    XPHistoryItem: obx_int.EntityDefinition<XPHistoryItem>(
      model: _entities[4],
      toOneRelations: (XPHistoryItem object) => [],
      toManyRelations: (XPHistoryItem object) => {},
      getId: (XPHistoryItem object) => object.id,
      setId: (XPHistoryItem object, int id) {
        object.id = id;
      },
      objectToFB: (XPHistoryItem object, fb.Builder fbb) {
        final descriptionOffset = fbb.writeString(object.description);
        fbb.startTable(5);
        fbb.addInt64(0, object.id);
        fbb.addInt64(1, object.amount);
        fbb.addOffset(2, descriptionOffset);
        fbb.addInt64(3, object.createdAt.millisecondsSinceEpoch);
        fbb.finish(fbb.endTable());
        return object.id;
      },
      objectFromFB: (obx.Store store, ByteData fbData) {
        final buffer = fb.BufferContext(fbData);
        final rootOffset = buffer.derefObject(0);
        final idParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          4,
          0,
        );
        final amountParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          6,
          0,
        );
        final descriptionParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 8, '');
        final createdAtParam = DateTime.fromMillisecondsSinceEpoch(
          const fb.Int64Reader().vTableGet(buffer, rootOffset, 10, 0),
        );
        final object = XPHistoryItem(
          id: idParam,
          amount: amountParam,
          description: descriptionParam,
          createdAt: createdAtParam,
        );

        return object;
      },
    ),
    Challenge: obx_int.EntityDefinition<Challenge>(
      model: _entities[5],
      toOneRelations: (Challenge object) => [object.xpHistory],
      toManyRelations: (Challenge object) => {},
      getId: (Challenge object) => object.id,
      setId: (Challenge object, int id) {
        object.id = id;
      },
      objectToFB: (Challenge object, fb.Builder fbb) {
        final titleOffset = fbb.writeString(object.title);
        final descriptionOffset = fbb.writeString(object.description);
        final featureNameOffset = fbb.writeString(object.featureName);
        final conditionJsonOffset = fbb.writeString(object.conditionJson);
        final iconPathOffset = fbb.writeString(object.iconPath);
        fbb.startTable(11);
        fbb.addInt64(0, object.id);
        fbb.addOffset(1, titleOffset);
        fbb.addOffset(2, descriptionOffset);
        fbb.addOffset(3, featureNameOffset);
        fbb.addOffset(4, conditionJsonOffset);
        fbb.addOffset(5, iconPathOffset);
        fbb.addBool(6, object.isCompleted);
        fbb.addInt64(7, object.completedAt?.millisecondsSinceEpoch);
        fbb.addInt64(8, object.xp);
        fbb.addInt64(9, object.xpHistory.targetId);
        fbb.finish(fbb.endTable());
        return object.id;
      },
      objectFromFB: (obx.Store store, ByteData fbData) {
        final buffer = fb.BufferContext(fbData);
        final rootOffset = buffer.derefObject(0);
        final completedAtValue = const fb.Int64Reader().vTableGetNullable(
          buffer,
          rootOffset,
          18,
        );
        final idParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          4,
          0,
        );
        final titleParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 6, '');
        final descriptionParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 8, '');
        final featureNameParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 10, '');
        final conditionJsonParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 12, '');
        final iconPathParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 14, '');
        final isCompletedParam = const fb.BoolReader().vTableGet(
          buffer,
          rootOffset,
          16,
          false,
        );
        final completedAtParam = completedAtValue == null
            ? null
            : DateTime.fromMillisecondsSinceEpoch(completedAtValue);
        final xpParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          20,
          0,
        );
        final object = Challenge(
          id: idParam,
          title: titleParam,
          description: descriptionParam,
          featureName: featureNameParam,
          conditionJson: conditionJsonParam,
          iconPath: iconPathParam,
          isCompleted: isCompletedParam,
          completedAt: completedAtParam,
          xp: xpParam,
        );
        object.xpHistory.targetId = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          22,
          0,
        );
        object.xpHistory.attach(store);
        return object;
      },
    ),
  };

  return obx_int.ModelDefinition(model, bindings);
}

/// [Prayer] entity fields to define ObjectBox queries.
class Prayer_ {
  /// See [Prayer.id].
  static final id = obx.QueryIntegerProperty<Prayer>(
    _entities[0].properties[0],
  );

  /// See [Prayer.name].
  static final name = obx.QueryStringProperty<Prayer>(
    _entities[0].properties[1],
  );

  /// See [Prayer.isCompleted].
  static final isCompleted = obx.QueryBooleanProperty<Prayer>(
    _entities[0].properties[2],
  );

  /// See [Prayer.date].
  static final date = obx.QueryDateProperty<Prayer>(_entities[0].properties[3]);

  /// See [Prayer.xpHistory].
  static final xpHistory = obx.QueryRelationToOne<Prayer, XPHistoryItem>(
    _entities[0].properties[4],
  );
}

/// [Streak] entity fields to define ObjectBox queries.
class Streak_ {
  /// See [Streak.id].
  static final id = obx.QueryIntegerProperty<Streak>(
    _entities[1].properties[0],
  );

  /// See [Streak.count].
  static final count = obx.QueryIntegerProperty<Streak>(
    _entities[1].properties[1],
  );

  /// See [Streak.goal].
  static final goal = obx.QueryIntegerProperty<Streak>(
    _entities[1].properties[2],
  );

  /// See [Streak.date].
  static final date = obx.QueryDateProperty<Streak>(_entities[1].properties[3]);

  /// See [Streak.moodIntensity].
  static final moodIntensity = obx.QueryIntegerProperty<Streak>(
    _entities[1].properties[4],
  );

  /// See [Streak.createdAt].
  static final createdAt = obx.QueryDateProperty<Streak>(
    _entities[1].properties[5],
  );

  /// See [Streak.lastUpdated].
  static final lastUpdated = obx.QueryDateProperty<Streak>(
    _entities[1].properties[6],
  );

  /// See [Streak.isSuccess].
  static final isSuccess = obx.QueryBooleanProperty<Streak>(
    _entities[1].properties[7],
  );

  /// See [Streak.xpHistory].
  static final xpHistory = obx.QueryRelationToOne<Streak, XPHistoryItem>(
    _entities[1].properties[8],
  );
}

/// [UserProfile] entity fields to define ObjectBox queries.
class UserProfile_ {
  /// See [UserProfile.id].
  static final id = obx.QueryIntegerProperty<UserProfile>(
    _entities[2].properties[0],
  );

  /// See [UserProfile.name].
  static final name = obx.QueryStringProperty<UserProfile>(
    _entities[2].properties[1],
  );

  /// See [UserProfile.goals].
  static final goals = obx.QueryStringVectorProperty<UserProfile>(
    _entities[2].properties[2],
  );

  /// See [UserProfile.hobbies].
  static final hobbies = obx.QueryStringVectorProperty<UserProfile>(
    _entities[2].properties[3],
  );

  /// See [UserProfile.triggers].
  static final triggers = obx.QueryStringVectorProperty<UserProfile>(
    _entities[2].properties[4],
  );

  /// See [UserProfile.streakGoal].
  static final streakGoal = obx.QueryIntegerProperty<UserProfile>(
    _entities[2].properties[5],
  );

  /// See [UserProfile.passwordHash].
  static final passwordHash = obx.QueryStringProperty<UserProfile>(
    _entities[2].properties[6],
  );

  /// See [UserProfile.biometricEnabled].
  static final biometricEnabled = obx.QueryBooleanProperty<UserProfile>(
    _entities[2].properties[7],
  );

  /// See [UserProfile.notificationsEnabled].
  static final notificationsEnabled = obx.QueryBooleanProperty<UserProfile>(
    _entities[2].properties[8],
  );

  /// See [UserProfile.createdAt].
  static final createdAt = obx.QueryDateProperty<UserProfile>(
    _entities[2].properties[9],
  );

  /// See [UserProfile.lastUpdated].
  static final lastUpdated = obx.QueryDateProperty<UserProfile>(
    _entities[2].properties[10],
  );

  /// See [UserProfile.profilePicture].
  static final profilePicture = obx.QueryStringProperty<UserProfile>(
    _entities[2].properties[11],
  );

  /// See [UserProfile.xp].
  static final xp = obx.QueryIntegerProperty<UserProfile>(
    _entities[2].properties[12],
  );
}

/// [Temptation] entity fields to define ObjectBox queries.
class Temptation_ {
  /// See [Temptation.id].
  static final id = obx.QueryIntegerProperty<Temptation>(
    _entities[3].properties[0],
  );

  /// See [Temptation.createdAt].
  static final createdAt = obx.QueryDateProperty<Temptation>(
    _entities[3].properties[1],
  );

  /// See [Temptation.resolvedAt].
  static final resolvedAt = obx.QueryDateProperty<Temptation>(
    _entities[3].properties[2],
  );

  /// See [Temptation.triggers].
  static final triggers = obx.QueryStringVectorProperty<Temptation>(
    _entities[3].properties[3],
  );

  /// See [Temptation.helpfulActivities].
  static final helpfulActivities = obx.QueryStringVectorProperty<Temptation>(
    _entities[3].properties[4],
  );

  /// See [Temptation.selectedActivity].
  static final selectedActivity = obx.QueryStringProperty<Temptation>(
    _entities[3].properties[5],
  );

  /// See [Temptation.wasSuccessful].
  static final wasSuccessful = obx.QueryBooleanProperty<Temptation>(
    _entities[3].properties[6],
  );

  /// See [Temptation.resolutionNotes].
  static final resolutionNotes = obx.QueryStringProperty<Temptation>(
    _entities[3].properties[7],
  );

  /// See [Temptation.intensityBefore].
  static final intensityBefore = obx.QueryIntegerProperty<Temptation>(
    _entities[3].properties[8],
  );

  /// See [Temptation.intensityAfter].
  static final intensityAfter = obx.QueryIntegerProperty<Temptation>(
    _entities[3].properties[9],
  );

  /// See [Temptation.xpHistory].
  static final xpHistory = obx.QueryRelationToOne<Temptation, XPHistoryItem>(
    _entities[3].properties[10],
  );
}

/// [XPHistoryItem] entity fields to define ObjectBox queries.
class XPHistoryItem_ {
  /// See [XPHistoryItem.id].
  static final id = obx.QueryIntegerProperty<XPHistoryItem>(
    _entities[4].properties[0],
  );

  /// See [XPHistoryItem.amount].
  static final amount = obx.QueryIntegerProperty<XPHistoryItem>(
    _entities[4].properties[1],
  );

  /// See [XPHistoryItem.description].
  static final description = obx.QueryStringProperty<XPHistoryItem>(
    _entities[4].properties[2],
  );

  /// See [XPHistoryItem.createdAt].
  static final createdAt = obx.QueryDateProperty<XPHistoryItem>(
    _entities[4].properties[3],
  );
}

/// [Challenge] entity fields to define ObjectBox queries.
class Challenge_ {
  /// See [Challenge.id].
  static final id = obx.QueryIntegerProperty<Challenge>(
    _entities[5].properties[0],
  );

  /// See [Challenge.title].
  static final title = obx.QueryStringProperty<Challenge>(
    _entities[5].properties[1],
  );

  /// See [Challenge.description].
  static final description = obx.QueryStringProperty<Challenge>(
    _entities[5].properties[2],
  );

  /// See [Challenge.featureName].
  static final featureName = obx.QueryStringProperty<Challenge>(
    _entities[5].properties[3],
  );

  /// See [Challenge.conditionJson].
  static final conditionJson = obx.QueryStringProperty<Challenge>(
    _entities[5].properties[4],
  );

  /// See [Challenge.iconPath].
  static final iconPath = obx.QueryStringProperty<Challenge>(
    _entities[5].properties[5],
  );

  /// See [Challenge.isCompleted].
  static final isCompleted = obx.QueryBooleanProperty<Challenge>(
    _entities[5].properties[6],
  );

  /// See [Challenge.completedAt].
  static final completedAt = obx.QueryDateProperty<Challenge>(
    _entities[5].properties[7],
  );

  /// See [Challenge.xp].
  static final xp = obx.QueryIntegerProperty<Challenge>(
    _entities[5].properties[8],
  );

  /// See [Challenge.xpHistory].
  static final xpHistory = obx.QueryRelationToOne<Challenge, XPHistoryItem>(
    _entities[5].properties[9],
  );
}
