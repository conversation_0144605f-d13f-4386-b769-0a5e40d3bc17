import 'package:flutter/material.dart';
import 'package:escape/theme/app_constants.dart';

class ArticleThumbnail extends StatelessWidget {
  final String? imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final VoidCallback? onTap;

  const ArticleThumbnail({
    super.key,
    this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final thumbnail = Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: AppConstants.lightGray,
        borderRadius: BorderRadius.circular(AppConstants.radiusM),
        image: imageUrl != null
            ? DecorationImage(image: NetworkImage(imageUrl!), fit: fit)
            : null,
      ),
      child: imageUrl == null
          ? Icon(Icons.image_outlined, color: AppConstants.mediumGray, size: 40)
          : null,
    );

    if (onTap != null) {
      return GestureDetector(onTap: onTap, child: thumbnail);
    }

    return thumbnail;
  }
}
