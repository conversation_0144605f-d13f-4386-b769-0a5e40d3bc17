name: escape
description: "A blessed Islamic app helping Muslims worldwide break free from pornography, masturbation, and spiritual darkness through Quranic guidance, streak tracking, and community support."

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

  # State Management
  flutter_riverpod: ^3.0.0-dev.16
  riverpod_annotation: ^3.0.0-dev.16

  # Local Storage
  objectbox: ^4.3.0
  objectbox_flutter_libs: ^4.3.0
  path: ^1.9.0
  path_provider: ^2.1.5

  # Backend
  supabase_flutter: ^2.10.0

  # UI & Design
  mix: ^1.7.0
  adaptive_dialog: ^2.4.2
  flutter_svg: ^2.2.0

  # Image picker for profile pictures
  image_picker: ^1.1.2

  # Google Fonts for beautiful typography
  google_fonts: ^6.2.1
  
  # Carousel for tips
  carousel_slider: ^5.0.0

  # Charting
  fl_chart: ^1.0.0

  # Markdown rendering
  flutter_markdown_plus: ^1.0.5

  # Video/Audio handling
  youtube_player_flutter: ^9.1.2
  audioplayers: ^6.5.1

  # Image handling
  cached_network_image: ^3.4.1

  # Utilities
  intl: ^0.20.2
  shared_preferences: ^2.5.3
  
  flutter_localizations:
    sdk: flutter
  async: ^2.13.0
  envied: ^1.2.1
  http: ^1.5.0
  flutter_local_notifications: ^19.4.2

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^6.0.0

  # Build runners
  build_runner: ^2.5.4
  custom_lint: ^0.7.6
  riverpod_generator: ^3.0.0-dev.16
  riverpod_lint: ^3.0.0-dev.16
  objectbox_generator: ^4.3.0
  envied_generator: ^1.1.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - assets/images/
  #   - assets/icons/
  #   - assets/videos/
  #   - assets/fonts/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
